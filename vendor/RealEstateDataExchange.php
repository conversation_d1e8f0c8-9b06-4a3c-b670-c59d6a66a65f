<?php
class Metainfo {
  public $source; // string
  public $destination; // string
  public $generationTime; // dateTime
  public $numberOfItems; // integer
  public $supportContactInfo; // string
}

class Brokers {
  public $Metainfo; // Metainfo
  public $Broker; // Broker
}

class Broker {
  public $originalId; // string
  public $companyId; // string
  public $status; // StatusType
  public $licenceNumber; // string
  public $name; // string
  public $phone; // string
  public $mobile; // string
  public $email; // string
  public $pictureUrl; // anyURI
  public $fax; // string
  public $skype; // string
  public $msn; // string
}

class Country {
}

class Projects {
  public $Metainfo; // Metainfo
  public $Project; // Project
}

class Project {
  public $originalId; // integer
  public $lastModified; // dateTime
  public $status; // StatusType
  public $Country; // Country
  public $level1; // string
  public $level2; // string
  public $level3; // string
  public $level4; // string
  public $level5; // string
  public $level6; // string
  public $postalCode; // string
  public $street; // HideableStringType
  public $houseNumber; // HideableStringType
  public $apartmentNumber; // HideableStringType
  public $geoCoordinates; // string
  public $numberOfFloors; // integer
  public $constructionStartDate; // date
  public $constructionEndDate; // date
  public $info; // InfoType
  public $constructionInfo; // InfoType
  public $interiorInfo; // InfoType
  public $salesInfo; // InfoType
  public $pictures; // PicturesType
  public $maps; // MapsType
  public $virtualTours; // VirtualToursType
  public $videos; // VideosType
  public $plans; // PlansType
  public $distances; // DistancesType
  public $childObjects; // ChildObjectsType
  public $childProjects; // ChildProjectsType
  public $Brokers; // BrokersType
  public $homepageUrl; // anyURI
  public $areaSize; // AreaSizeType
  public $landSize; // AreaSizeType
  public $realEstateNumber; // string
  public $cadastralNumbers; // CadastralNumbersType
  public $buildingStructure; // BuildingStructureType
  public $roads; // RoadsType
  public $objectsSummary; // ObjectSummaryType
  public $architect; // string
  public $constructor; // string
}

class Objects {
  public $Metainfo; // Metainfo
  public $Object; // Object
}

class Object_t {
  public $objectTypes; // ObjectTypesType
  public $originalId; // integer
  public $Brokers; // BrokersType
  public $lastModified; // dateTime
  public $status; // StatusType
  public $statusDescriptor; // statusDescriptor
  public $transactions; // TransactionsType
  public $visible; // visible
  public $advance; // PriceType
  public $isBooked; // boolean
  public $bookedUntil; // date
  public $Country; // Country
  public $level1; // string
  public $level2; // string
  public $level3; // string
  public $level4; // string
  public $level5; // string
  public $level6; // string
  public $postalCode; // string
  public $street; // HideableStringType
  public $houseNumber; // HideableStringType
  public $apartmentNumber; // HideableStringType
  public $geoCoordinates; // string
  public $priority; // priority
  public $numberOfFloors; // integer
  public $floorNumber; // integer
  public $buildYear; // gYear
  public $pictures; // PicturesType
  public $maps; // MapsType
  public $virtualTours; // VirtualToursType
  public $videos; // VideosType
  public $plans; // PlansType
  public $attachedFiles; // AttachedFilesType
  public $distances; // DistancesType
  public $childObjects; // ChildObjectsType
  public $homepageUrl; // anyURI
  public $alacrity; // alacrity
  public $areaSize; // AreaSizeType
  public $landSize; // AreaSizeType
  public $propertyType; // propertyType
  public $limitations; // limitations
  public $numberOfPhones; // integer
  public $roof; // roof
  public $realEstateNumber; // int
  public $cadastralNumbers; // CadastralNumbersType
  public $parking; // ParkingType
  public $kitchenAreaSize; // AreaSizeType
  public $numberOfRooms; // integer
  public $numberOfBedrooms; // integer
  public $numberOfBathrooms; // integer
  public $numberOfWcs; // integer
  public $numberOfLivingrooms; // integer
  public $numberOfKitchens; // integer
  public $buildingStructure; // BuildingStructureType
  public $stove; // stove
  public $balconies; // balconies
  public $sewer; // SewerType
  public $doors; // doors
  public $windows; // windows
  public $heatings; // heatings
  public $roads; // RoadsType
  public $renovations; // renovations
  public $neighbours; // neighbours
  public $options; // options
  public $title;
  public $brokerName;
}

class statusDescriptor {
}

class visible {
}

class priority {
}

class alacrity {
}

class propertyType {
}

class limitations {
  public $limitation; // limitation
}

class limitation {
}

class roof {
}

class stove {
}

class balconies {
  public $balcony; // balcony
}

class balcony {
  public $size; // AreaSizeType
  public $type; // BalconyTypeType
}

class doors {
  public $frontDoor; // DoorType
  public $interiorDoor; // DoorType
}

class windows {
  public $window; // window
}

class window {
}

class heatings {
  public $heating; // heating
}

class heating {
}

class renovations {
  public $renovation; // RenovationYearType
}

class neighbours {
  public $neighbour; // neighbour
}

class neighbour {
}

class options {
  public $option; // option
}

class option {
  public $_; // OptionEnum
  public $count; // integer
}

class ObjectTypeType {
}

class StatusType {
}

class OptionEnum {
}

class HideableStringType {
  public $_; // string
  public $hidden; // boolean
}

class MediaUrlType {
  public $_; // OrderedUrlType
  public $size; // nonNegativeInteger
}

class PicturesType {
  public $pictureUrl; // MediaUrlType
}

class PlanType {
  public $_; // anyURI
  public $title; // string
  public $floor; // integer
  public $format; // string
  public $size; // nonNegativeInteger
}

class OrderedUrlType {
  public $_; // anyURI
  public $title; // string
  public $order; // integer
}

class PriceType {
  public $_; // double
  public $currency; // string
  public $public; // boolean
}

class DoorType {
}

class InfoType {
  public $_; // string
  public $language; // string
}

class AreaSizeType {
  public $_; // double
  public $unit; // anonymous
}

class anonymous {
}

class RenovationEnum {
}

class RenovationYearType {
  public $_; // RenovationEnum
  public $year; // gYear
}

class BuildingStructureType {
}

class RoadType {
}

class SewerType {
}

class MapType {
  public $provider; // provider
  public $area; // string
  public $point; // string
  public $zoomLevel; // string
  public $pictureUrl; // anyURI
}

class provider {
}

class DistanceFromType {
  public $Country; // Country
  public $level1; // string
  public $level2; // string
  public $level3; // string
  public $level4; // string
  public $level5; // string
  public $level6; // string
  public $postalCode; // string
  public $street; // HideableStringType
  public $houseNumber; // HideableStringType
  public $apartmentNumber; // HideableStringType
  public $geoCoordinates; // string
  public $distanceInKm; // integer
  public $description; // InfoType
}

class Companies {
  public $Metainfo; // Metainfo
  public $Company; // Company
}

class Company {
  public $originalId; // string
  public $status; // StatusType
  public $name; // string
  public $branch; // string
  public $logoUrl; // anyURI
  public $homepageUrl; // anyURI
  public $telephone; // string
  public $registryNumber; // string
  public $fax; // string
  public $VATNumber; // string
  public $Country; // Country
  public $level1; // string
  public $level2; // string
  public $level3; // string
  public $level4; // string
  public $level5; // string
  public $level6; // string
  public $postalCode; // string
  public $street; // HideableStringType
  public $houseNumber; // HideableStringType
  public $apartmentNumber; // HideableStringType
  public $geoCoordinates; // string
}

class MapsType {
  public $map; // MapType
}

class VirtualToursType {
  public $virtualTourUrl; // OrderedUrlType
}

class ParkingType {
  public $type; // ParkingLocationType
  public $count; // integer
  public $isParkingForPrice; // boolean
  public $isParkingForFree; // boolean
  public $description; // InfoType
}

class ParkingLocationType {
}

class VideosType {
  public $videoUrl; // MediaUrlType
}

class AttachedFilesType {
  public $File; // MediaUrlType
}

class PlansType {
  public $plan; // PlanType
}

class DistancesType {
  public $distanceFrom; // DistanceFromType
}

class ChildObjectsType {
  public $originalId; // integer
}

class ChildProjectsType {
  public $originalId; // integer
}

class BrokersType {
  public $originalId; // string
}

class CadastralNumbersType {
  public $cadastralNumber; // string
}

class RoadsType {
  public $road; // RoadType
}

class BalconyTypeType {
}

class ObjectGroupType {
  public $type; // ObjectTypeType
  public $numberOfRooms; // positiveInteger
  public $minSize; // AreaSizeType
  public $maxSize; // AreaSizeType
  public $minPrice; // PriceType
  public $maxPrice; // PriceType
  public $description; // InfoType
}

class ObjectSummaryType {
  public $objectGroup; // ObjectGroupType
}

class ObjectTypesType {
  public $type; // ObjectTypeType
}

class TransactionType {
  public $type; // TransactionTypeType
  public $price; // PriceType
  public $bargainPrice; // PriceType
  public $info; // InfoType
  public $technicalInfo; // InfoType
  public $viewedTimes; // integer
  public $expireDate; // date
  public $externalId; // integer
  public $externalUrl; // anyURI
}

class TransactionTypeType {
}

class TransactionsType {
  public $transaction; // TransactionType
}

class getCompanyIdsByStatus {
  public $username; // string
  public $password; // string
  public $status; // StatusType
}

class getCompanyIdsByStatusResponse {
  public $Metainfo; // Metainfo
  public $originalId; // integer
}

class getBrokerIdsByStatus {
  public $username; // string
  public $password; // string
  public $status; // StatusType
}

class getBrokerIdsByStatusResponse {
  public $Metainfo; // Metainfo
  public $originalId; // integer
}

class getObjectIdsByStatus {
  public $username; // string
  public $password; // string
  public $status; // StatusType
}

class getObjectIdsByStatusResponse {
  public $Metainfo; // Metainfo
  public $originalId; // integer
}

class getProjectIdsByStatus {
  public $username; // string
  public $password; // string
  public $status; // StatusType
}

class getProjectIdsByStatusResponse {
  public $Metainfo; // Metainfo
  public $originalId; // integer
}

class getBrokerById {
  public $username; // string
  public $password; // string
  public $originalId; // integer
}

class getBrokerByIdResponse {
  public $Metainfo; // Metainfo
  public $Broker; // Broker
}

class getCompanyById {
  public $username; // string
  public $password; // string
  public $originalId; // integer
}

class getCompanyByIdResponse {
  public $Metainfo; // Metainfo
  public $Company; // Company
}

class getObjectById {
  public $username; // string
  public $password; // string
  public $originalId; // integer
}

class getObjectByIdResponse {
  public $Metainfo; // Metainfo
  public $Object; // Object
}

class getProjectById {
  public $username; // string
  public $password; // string
  public $originalId; // integer
}

class getProjectByIdResponse {
  public $Metainfo; // Metainfo
  public $Project; // Project
}

class updateBrokerById {
  public $username; // string
  public $password; // string
  public $originalId; // integer
}

class updateCompanyById {
  public $username; // string
  public $password; // string
  public $originalId; // integer
}

class updateObjectById {
  public $username; // string
  public $password; // string
  public $originalId; // integer
}

class updateProjectById {
  public $username; // string
  public $password; // string
  public $originalId; // integer
}

class brokerUpdateCompleted {
  public $username; // string
  public $password; // string
  public $Brokers; // Brokers
}

class companyUpdateCompleted {
  public $username; // string
  public $password; // string
  public $Companies; // Companies
}

class objectUpdateCompleted {
  public $username; // string
  public $password; // string
  public $Objects; // Objects
}

class projectUpdateCompleted {
  public $username; // string
  public $password; // string
  public $Projects; // Projects
}

class getCompanyIdsModifiedAfter {
  public $username; // string
  public $password; // string
  public $time; // dateTime
}

class getCompanyIdsModifiedAfterResponse {
  public $Metainfo; // Metainfo
  public $originalId; // integer
}

class getBrokerIdsModifiedAfter {
  public $username; // string
  public $password; // string
  public $time; // dateTime
}

class getBrokerIdsModifiedAfterResponse {
  public $Metainfo; // Metainfo
  public $originalId; // integer
}

class getObjectIdsModifiedAfter {
  public $username; // string
  public $password; // string
  public $time; // dateTime
}

class getObjectIdsModifiedAfterResponse {
  public $Metainfo; // Metainfo
  public $originalId; // integer
}

class getProjectIdsModifiedAfter {
  public $username; // string
  public $password; // string
  public $time; // dateTime
}

class getProjectIdsModifiedAfterResponse {
  public $Metainfo; // Metainfo
  public $originalId; // integer
}


/**
 * RealEstateDataExchange class
 *
 *
 *
 * <AUTHOR>
 * @copyright {copyright}
 * @package   {package}
 */
class RealEstateDataExchange extends SoapClient {

  private static $classmap = array(
                                    'Metainfo' => 'Metainfo',
                                    'Brokers' => 'Brokers',
                                    'Broker' => 'Broker',
                                    'Country' => 'Country',
                                    'Projects' => 'Projects',
                                    'Project' => 'Project',
                                    'Objects' => 'Objects',
                                    'Object' => 'Object',
                                    'statusDescriptor' => 'statusDescriptor',
                                    'visible' => 'visible',
                                    'priority' => 'priority',
                                    'alacrity' => 'alacrity',
                                    'propertyType' => 'propertyType',
                                    'limitations' => 'limitations',
                                    'limitation' => 'limitation',
                                    'roof' => 'roof',
                                    'stove' => 'stove',
                                    'balconies' => 'balconies',
                                    'balcony' => 'balcony',
                                    'doors' => 'doors',
                                    'windows' => 'windows',
                                    'window' => 'window',
                                    'heatings' => 'heatings',
                                    'heating' => 'heating',
                                    'renovations' => 'renovations',
                                    'neighbours' => 'neighbours',
                                    'neighbour' => 'neighbour',
                                    'options' => 'options',
                                    'option' => 'option',
                                    'ObjectTypeType' => 'ObjectTypeType',
                                    'StatusType' => 'StatusType',
                                    'OptionEnum' => 'OptionEnum',
                                    'HideableStringType' => 'HideableStringType',
                                    'MediaUrlType' => 'MediaUrlType',
                                    'PicturesType' => 'PicturesType',
                                    'PlanType' => 'PlanType',
                                    'OrderedUrlType' => 'OrderedUrlType',
                                    'PriceType' => 'PriceType',
                                    'DoorType' => 'DoorType',
                                    'InfoType' => 'InfoType',
                                    'AreaSizeType' => 'AreaSizeType',
                                    'anonymous' => 'anonymous',
                                    'RenovationEnum' => 'RenovationEnum',
                                    'RenovationYearType' => 'RenovationYearType',
                                    'BuildingStructureType' => 'BuildingStructureType',
                                    'RoadType' => 'RoadType',
                                    'SewerType' => 'SewerType',
                                    'MapType' => 'MapType',
                                    'provider' => 'provider',
                                    'DistanceFromType' => 'DistanceFromType',
                                    'Companies' => 'Companies',
                                    'Company' => 'Company',
                                    'MapsType' => 'MapsType',
                                    'VirtualToursType' => 'VirtualToursType',
                                    'ParkingType' => 'ParkingType',
                                    'ParkingLocationType' => 'ParkingLocationType',
                                    'VideosType' => 'VideosType',
                                    'AttachedFilesType' => 'AttachedFilesType',
                                    'PlansType' => 'PlansType',
                                    'DistancesType' => 'DistancesType',
                                    'ChildObjectsType' => 'ChildObjectsType',
                                    'ChildProjectsType' => 'ChildProjectsType',
                                    'BrokersType' => 'BrokersType',
                                    'CadastralNumbersType' => 'CadastralNumbersType',
                                    'RoadsType' => 'RoadsType',
                                    'BalconyTypeType' => 'BalconyTypeType',
                                    'ObjectGroupType' => 'ObjectGroupType',
                                    'ObjectSummaryType' => 'ObjectSummaryType',
                                    'ObjectTypesType' => 'ObjectTypesType',
                                    'TransactionType' => 'TransactionType',
                                    'TransactionTypeType' => 'TransactionTypeType',
                                    'TransactionsType' => 'TransactionsType',
                                    'getCompanyIdsByStatus' => 'getCompanyIdsByStatus',
                                    'getCompanyIdsByStatusResponse' => 'getCompanyIdsByStatusResponse',
                                    'getBrokerIdsByStatus' => 'getBrokerIdsByStatus',
                                    'getBrokerIdsByStatusResponse' => 'getBrokerIdsByStatusResponse',
                                    'getObjectIdsByStatus' => 'getObjectIdsByStatus',
                                    'getObjectIdsByStatusResponse' => 'getObjectIdsByStatusResponse',
                                    'getProjectIdsByStatus' => 'getProjectIdsByStatus',
                                    'getProjectIdsByStatusResponse' => 'getProjectIdsByStatusResponse',
                                    'getBrokerById' => 'getBrokerById',
                                    'getBrokerByIdResponse' => 'getBrokerByIdResponse',
                                    'getCompanyById' => 'getCompanyById',
                                    'getCompanyByIdResponse' => 'getCompanyByIdResponse',
                                    'getObjectById' => 'getObjectById',
                                    'getObjectByIdResponse' => 'getObjectByIdResponse',
                                    'getProjectById' => 'getProjectById',
                                    'getProjectByIdResponse' => 'getProjectByIdResponse',
                                    'updateBrokerById' => 'updateBrokerById',
                                    'updateCompanyById' => 'updateCompanyById',
                                    'updateObjectById' => 'updateObjectById',
                                    'updateProjectById' => 'updateProjectById',
                                    'brokerUpdateCompleted' => 'brokerUpdateCompleted',
                                    'companyUpdateCompleted' => 'companyUpdateCompleted',
                                    'objectUpdateCompleted' => 'objectUpdateCompleted',
                                    'projectUpdateCompleted' => 'projectUpdateCompleted',
                                    'getCompanyIdsModifiedAfter' => 'getCompanyIdsModifiedAfter',
                                    'getCompanyIdsModifiedAfterResponse' => 'getCompanyIdsModifiedAfterResponse',
                                    'getBrokerIdsModifiedAfter' => 'getBrokerIdsModifiedAfter',
                                    'getBrokerIdsModifiedAfterResponse' => 'getBrokerIdsModifiedAfterResponse',
                                    'getObjectIdsModifiedAfter' => 'getObjectIdsModifiedAfter',
                                    'getObjectIdsModifiedAfterResponse' => 'getObjectIdsModifiedAfterResponse',
                                    'getProjectIdsModifiedAfter' => 'getProjectIdsModifiedAfter',
                                    'getProjectIdsModifiedAfterResponse' => 'getProjectIdsModifiedAfterResponse',
                                   );

  public function RealEstateDataExchange($wsdl = "REDE.wsdl", $options = array()) {
    foreach(self::$classmap as $key => $value) {
      if(!isset($options['classmap'][$key])) {
        $options['classmap'][$key] = $value;
      }
    }
    parent::__construct($wsdl, $options);
  }

  /**
   *
   *
   * @param getCompanyIdsByStatus $parameters
   * @return getCompanyIdsByStatusResponse
   */
  public function getCompanyIdsByStatus($parameters) {
    return $this->__soapCall('getCompanyIdsByStatus', array($parameters),       array(
            'uri' => 'urn:REDE/0.93',
            'soapaction' => ''
           )
      );
  }

  /**
   *
   *
   * @param getBrokerIdsByStatus $parameters
   * @return getBrokerIdsByStatusResponse
   */
  public function getBrokerIdsByStatus($parameters) {
    return $this->__soapCall('getBrokerIdsByStatus', array($parameters),       array(
            'uri' => 'urn:REDE/0.93',
            'soapaction' => ''
           )
      );
  }

  /**
   *
   *
   * @param getObjectIdsByStatus $parameters
   * @return getObjectIdsByStatusResponse
   */
  public function getObjectIdsByStatus($parameters) {
    return $this->__soapCall('getObjectIdsByStatus', array($parameters),       array(
            'uri' => 'urn:REDE/0.93',
            'soapaction' => ''
           )
      );
  }

  /**
   *
   *
   * @param getProjectIdsByStatus $parameters
   * @return getProjectIdsByStatusResponse
   */
  public function getProjectIdsByStatus($parameters) {
    return $this->__soapCall('getProjectIdsByStatus', array($parameters),       array(
            'uri' => 'urn:REDE/0.93',
            'soapaction' => ''
           )
      );
  }

  /**
   * Returns brokers tag with metainfo and one broker in it. Broker tag usually contains the
   * broker's contact
                information and picture.
   *
   * @param getBrokerById $parameters
   * @return getBrokerByIdResponse
   */
  public function getBrokerById($parameters) {
    return $this->__soapCall('getBrokerById', array($parameters),       array(
            'uri' => 'urn:REDE/0.93',
            'soapaction' => ''
           )
      );
  }

  /**
   * Returns companies tag with metainfo and company tag in it. Company tag usually contains
   * company contact
                information and logo and homepage urls.
   *
   * @param getCompanyById $parameters
   * @return getCompanyByIdResponse
   */
  public function getCompanyById($parameters) {
    return $this->__soapCall('getCompanyById', array($parameters),       array(
            'uri' => 'urn:REDE/0.93',
            'soapaction' => ''
           )
      );
  }

  /**
   *
   *
   * @param getObjectById $parameters
   * @return getObjectByIdResponse
   */
  public function getObjectById(getObjectById $parameters) {
    return $this->__soapCall('getObjectById', array($parameters),       array(
            'uri' => 'urn:REDE/0.93',
            'soapaction' => ''
           )
      );
  }

  /**
   *
   *
   * @param getProjectById $parameters
   * @return getProjectByIdResponse
   */
  public function getProjectById($parameters) {
    return $this->__soapCall('getProjectById', array($parameters),       array(
            'uri' => 'urn:REDE/0.93',
            'soapaction' => ''
           )
      );
  }

  /**
   *
   *
   * @param updateBrokerById $parameters
   * @return string
   */
  public function updateBrokerById($parameters) {
    return $this->__soapCall('updateBrokerById', array($parameters),       array(
            'uri' => 'urn:REDE/0.93',
            'soapaction' => ''
           )
      );
  }

  /**
   *
   *
   * @param updateCompanyById $parameters
   * @return string
   */
  public function updateCompanyById($parameters) {
    return $this->__soapCall('updateCompanyById', array($parameters),       array(
            'uri' => 'urn:REDE/0.93',
            'soapaction' => ''
           )
      );
  }

  /**
   *
   *
   * @param updateObjectById $parameters
   * @return string
   */
  public function updateObjectById($parameters) {
    return $this->__soapCall('updateObjectById', array($parameters),       array(
            'uri' => 'urn:REDE/0.93',
            'soapaction' => ''
           )
      );
  }

  /**
   *
   *
   * @param updateProjectById $parameters
   * @return string
   */
  public function updateProjectById($parameters) {
    return $this->__soapCall('updateProjectById', array($parameters),       array(
            'uri' => 'urn:REDE/0.93',
            'soapaction' => ''
           )
      );
  }

  /**
   *
   *
   * @param brokerUpdateCompleted $parameters
   * @return string
   */
  public function brokerUpdateCompleted($parameters) {
    return $this->__soapCall('brokerUpdateCompleted', array($parameters),       array(
            'uri' => 'urn:REDE/0.93',
            'soapaction' => ''
           )
      );
  }

  /**
   *
   *
   * @param companyUpdateCompleted $parameters
   * @return string
   */
  public function companyUpdateCompleted($parameters) {
    return $this->__soapCall('companyUpdateCompleted', array($parameters),       array(
            'uri' => 'urn:REDE/0.93',
            'soapaction' => ''
           )
      );
  }

  /**
   *
   *
   * @param objectUpdateCompleted $parameters
   * @return string
   */
  public function objectUpdateCompleted($parameters) {
    return $this->__soapCall('objectUpdateCompleted', array($parameters),       array(
            'uri' => 'urn:REDE/0.93',
            'soapaction' => ''
           )
      );
  }

  /**
   *
   *
   * @param projectUpdateCompleted $parameters
   * @return string
   */
  public function projectUpdateCompleted($parameters) {
    return $this->__soapCall('projectUpdateCompleted', array($parameters),       array(
            'uri' => 'urn:REDE/0.93',
            'soapaction' => ''
           )
      );
  }

  /**
   *
   *
   * @param getCompanyIdsModifiedAfter $parameters
   * @return getCompanyIdsModifiedAfterResponse
   */
  public function getCompanyIdsModifiedAfter($parameters) {
    return $this->__soapCall('getCompanyIdsModifiedAfter', array($parameters),       array(
            'uri' => 'urn:REDE/0.93',
            'soapaction' => ''
           )
      );
  }

  /**
   *
   *
   * @param getBrokerIdsModifiedAfter $parameters
   * @return getBrokerIdsModifiedAfterResponse
   */
  public function getBrokerIdsModifiedAfter($parameters) {
    return $this->__soapCall('getBrokerIdsModifiedAfter', array($parameters),       array(
            'uri' => 'urn:REDE/0.93',
            'soapaction' => ''
           )
      );
  }

  /**
   *
   *
   * @param getObjectIdsModifiedAfter $parameters
   * @return getObjectIdsModifiedAfterResponse
   */
  public function getObjectIdsModifiedAfter($parameters) {
    return $this->__soapCall('getObjectIdsModifiedAfter', array($parameters),       array(
            'uri' => 'urn:REDE/0.93',
            'soapaction' => ''
           )
      );
  }

  /**
   *
   *
   * @param getProjectIdsModifiedAfter $parameters
   * @return getProjectIdsModifiedAfterResponse
   */
  public function getProjectIdsModifiedAfter($parameters) {
    return $this->__soapCall('getProjectIdsModifiedAfter', array($parameters),       array(
            'uri' => 'urn:REDE/0.93',
            'soapaction' => ''
           )
      );
  }

}
