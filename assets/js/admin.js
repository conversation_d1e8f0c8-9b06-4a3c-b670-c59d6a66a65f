jQuery(function($) {
	$('body').on('click', '#nna-sync-objects', function() {
		$.ajax({
			url: ajaxurl,
			type: 'post',
			dataType: 'json',
			data: {
				action: 'nnaSyncObjects'
			},
			success: function(data) {
				console.log(data);
			}
		});
	});

	$('body').on('click', '#nna-sync-brenollis-objects', function() {
		console.log('go');
		$.ajax({
			url: ajaxurl,
			type: 'post',
			dataType: 'json',
			data: {
				action: 'nnaSyncBrenollisObjects'
			},
			success: function(data) {
				console.log(data);
			}
		});
	});

	$('body').on('click', '#nna-sync-users', function() {
		$.ajax({
			url: ajaxurl,
			type: 'post',
			dataType: 'json',
			data: {
				action: 'nnaSyncUsers'
			},
			success: function(data) {
				console.log(data);
			}
		});
	});
});
