<?php
if( php_sapi_name() !== 'cli' ) {
    die("Meant to be run from command line");
}

function find_wordpress_base_path() {
    $dir = dirname(__FILE__);
    do {
        //it is possible to check for other files here
        if( file_exists($dir."/wp-config.php") ) {
            return $dir;
        }
    } while( $dir = realpath("$dir/..") );
    return null;
}

define( 'BASE_PATH', find_wordpress_base_path()."/" );
define('WP_USE_THEMES', false);
global $wp, $wp_query, $wp_the_query, $wp_rewrite, $wp_did_header;
require(BASE_PATH . 'wp-load.php');

class NnaLimitedAPI extends NnaApi {
	private static $instance;
	public $limit = 1000;
	public $offset = 0;

	public static function getInstance()
    {
        if (null == self::$instance) {
            self::$instance = new self;
        }

        return self::$instance;
    }

    public function updateObjects($ids, $client)
    {
        $existing = array();
        $collection = $this->getCollection($this->getDbName().'.objects');
        $sobject=array();
        $sobject['Object.originalId']['$in']=$ids;
        $counter = 0;
        foreach ($collection->find($sobject, array('noCursorTimeout' => true)) as $object) {
            echo '.';
            $id = $object->Object->originalId;
            echo $id;
            $existing[] = $id;
        	if ($counter >= $this->offset && $counter < ($this->limit + $this->offset)) {
                echo 'checking ';
	            $params = $this->getWsdlParams('getObjectById');
	            $params->originalId = $id;
	            try {
	                $rs = $client->getObjectById($params);
	            } catch (SoapFault $e) {
	                $client = $this->getObjectsWsdl();
	                $rs = $client->getObjectById($params);
	            }

	            file_put_contents(NNA_PATH . '/log/synclog.log', 'checking: #'.$rs->Object->originalId.PHP_EOL, FILE_APPEND);

	            $ours=new DateTime($object->Object->lastModified);
	            $their=new DateTime($rs->Object->lastModified);
	            if($their>$ours){
                    echo 'updating ';
	                $this->updateObject($rs, $object, $collection);
	                $params = $this->getWsdlParams('objectUpdateCompleted');
	                $params->Objects = $rs;
	                $response = $client->objectUpdateCompleted($params);
	            }
        	}

            echo PHP_EOL;
            $counter++;
        }
        return array_diff($ids, $existing);
    }
}

$instance = NnaLimitedAPI::getInstance();
$instance->limit = 1000;
$instance->offset = 0;
$instance->startObjectSync(true);
